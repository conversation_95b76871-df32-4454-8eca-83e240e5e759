/**app.scss - 恋爱小程序全局样式**/

/* ===== 全局CSS变量 ===== */
page {
  /* 主色调 - 星空紫系列 */
  --primary-color: #8B5CF6;
  --primary-light: #A78BFA;
  --primary-dark: #7C3AED;

  /* 辅助色 - 奶油粉系列 */
  --secondary-color: #F8BBD9;
  --secondary-light: #FCE7F3;
  --secondary-dark: #EC4899;

  /* 雾霾蓝系列 */
  --accent-color: #94A3B8;
  --accent-light: #CBD5E1;
  --accent-dark: #64748B;

  /* 金色系列 */
  --gold-color: #F59E0B;
  --gold-light: #FCD34D;
  --gold-dark: #D97706;

  /* 中性色 */
  --text-primary: #1F2937;
  --text-secondary: #6B7280;
  --text-light: #9CA3AF;
  --text-white: #FFFFFF;

  /* 背景色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8FAFC;
  --bg-card: #FFFFFF;
  --bg-gradient-purple: linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%);
  --bg-gradient-pink: linear-gradient(135deg, #F8BBD9 0%, #FCE7F3 100%);
  --bg-gradient-blue: linear-gradient(135deg, #94A3B8 0%, #CBD5E1 100%);

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-card: 0 4px 20px rgba(139, 92, 246, 0.15);

  /* 圆角 */
  --radius-sm: 8rpx;
  --radius-md: 16rpx;
  --radius-lg: 24rpx;
  --radius-xl: 32rpx;
  --radius-full: 50%;

  /* 间距 */
  --space-xs: 8rpx;
  --space-sm: 16rpx;
  --space-md: 24rpx;
  --space-lg: 32rpx;
  --space-xl: 48rpx;
  --space-2xl: 64rpx;

  /* 字体大小 */
  --text-xs: 24rpx;
  --text-sm: 28rpx;
  --text-base: 32rpx;
  --text-lg: 36rpx;
  --text-xl: 40rpx;
  --text-2xl: 48rpx;
  --text-3xl: 56rpx;
}

/* ===== 全局重置样式 ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

page {
  background-color: var(--bg-secondary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  color: var(--text-primary);
  line-height: 1.6;
}

/* ===== 通用容器样式 ===== */
.container {
  padding: var(--space-md);
  min-height: 100vh;
}

.page-container {
  padding: var(--space-lg) var(--space-md);
  min-height: calc(100vh - 120rpx);
}

/* ===== 通用卡片样式 ===== */
.card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-card);
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
}

.card-gradient {
  background: var(--bg-gradient-purple);
  color: var(--text-white);
}

.card-soft {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  padding: var(--space-xl);
}

/* ===== 通用按钮样式 ===== */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: var(--bg-gradient-purple);
  color: var(--text-white);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-gradient-pink);
  color: var(--text-primary);
}

.btn-outline {
  background: transparent;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

.btn-ghost {
  background: rgba(139, 92, 246, 0.1);
  color: var(--primary-color);
}

/* ===== 文本样式 ===== */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-light {
  color: var(--text-light);
}

.text-white {
  color: var(--text-white);
}

.text-gradient {
  background: var(--bg-gradient-purple);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ===== 字体大小 ===== */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
