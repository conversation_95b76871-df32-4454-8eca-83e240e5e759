/* 自定义导航栏样式 - 恋爱小程序 */
.weui-navigation-bar {
  --weui-FG-0: var(--text-primary);
  --height: 88rpx;
  --left: 32rpx;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  color: var(--weui-FG-0);
  flex: none;
}

.weui-navigation-bar.android {
  --height: 96rpx;
}

/* 渐变背景变体 */
.weui-navigation-bar.gradient {
  background: var(--bg-gradient-purple);
  --weui-FG-0: var(--text-white);
}

.weui-navigation-bar__inner {
  position: relative;
  top: 0;
  left: 0;
  height: calc(var(--height) + env(safe-area-inset-top));
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-top: env(safe-area-inset-top);
  padding-left: var(--space-md);
  padding-right: var(--space-md);
  width: 100%;
  box-sizing: border-box;
}

.weui-navigation-bar__inner.ios {
  padding-top: calc(env(safe-area-inset-top) + 16rpx);
}

.weui-navigation-bar__inner.android {
  padding-top: calc(env(safe-area-inset-top) + 8rpx);
}

/* 左侧区域 */
.weui-navigation-bar__left {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  min-width: 120rpx;
  height: 100%;
  box-sizing: border-box;
}

.weui-navigation-bar__buttons {
  display: flex;
  align-items: center;
}

.weui-navigation-bar__btn_goback_wrapper,
.weui-navigation-bar__btn_home_wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-lg);
  background: rgba(139, 92, 246, 0.1);
  margin-right: var(--space-sm);
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
}

.weui-navigation-bar__btn_goback_wrapper:active,
.weui-navigation-bar__btn_home_wrapper:active {
  background: rgba(139, 92, 246, 0.2);
  transform: scale(0.95);
}

.weui-navigation-bar__btn_goback_wrapper.weui-active,
.weui-navigation-bar__btn_home_wrapper.weui-active {
  opacity: 0.7;
}

.weui-navigation-bar__button {
  width: 32rpx;
  height: 32rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.weui-navigation-bar__btn_goback {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238B5CF6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='m15 18-6-6 6-6'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  -webkit-mask: none;
  mask: none;
  background-color: transparent;
}

.weui-navigation-bar__btn_home {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238B5CF6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z'/%3E%3Cpolyline points='9,22 9,12 15,12 15,22'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 渐变背景下的按钮样式 */
.weui-navigation-bar.gradient .weui-navigation-bar__btn_goback_wrapper,
.weui-navigation-bar.gradient .weui-navigation-bar__btn_home_wrapper {
  background: rgba(255, 255, 255, 0.2);
}

.weui-navigation-bar.gradient .weui-navigation-bar__btn_goback {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='m15 18-6-6 6-6'/%3E%3C/svg%3E");
}

.weui-navigation-bar.gradient .weui-navigation-bar__btn_home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z'/%3E%3Cpolyline points='9,22 9,12 15,12 15,22'/%3E%3C/svg%3E");
}

/* 中间标题区域 */
.weui-navigation-bar__center {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--weui-FG-0);
  text-align: center;
  height: 100%;
  position: relative;
}

.weui-navigation-bar__loading {
  display: flex;
  align-items: center;
  margin-right: var(--space-sm);
}

.weui-loading {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid var(--accent-light);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: block;
  background: transparent;
  margin-left: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 右侧区域 */
.weui-navigation-bar__right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 120rpx;
  height: 100%;
}

/* 激活状态 */
.weui-active {
  opacity: 0.7;
}
