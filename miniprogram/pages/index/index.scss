/* 恋爱小程序首页样式 */

page {
  height: 100vh;
  background: var(--bg-secondary);
}

.page-container {
  background: linear-gradient(180deg, rgba(139, 92, 246, 0.05) 0%, var(--bg-secondary) 30%);
  padding-top: var(--space-lg);
  padding-bottom: 120rpx; /* 为底部导航栏留出空间 */
  min-height: 100vh;
}

/* 每日金句横幅 */
.daily-quote-banner {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  margin: var(--space-md);
  box-shadow: var(--shadow-card);
  position: relative;
  overflow: hidden;
}

.daily-quote-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8rpx;
  background: var(--bg-gradient-purple);
}

.quote-content {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--space-md);
}

.quote-icon {
  font-size: 48rpx;
  margin-right: var(--space-md);
  flex-shrink: 0;
}

.quote-text {
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--text-primary);
  font-weight: 500;
}

.quote-date {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  text-align: right;
}

/* 今日运势卡片 */
.fortune-card {
  margin: var(--space-md);
  position: relative;
  overflow: hidden;
}

.fortune-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
}

.fortune-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-white);
}

.fortune-symbol {
  font-size: var(--text-2xl);
}

.fortune-content {
  margin-bottom: var(--space-lg);
}

.fortune-text {
  font-size: var(--text-base);
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
}

.fortune-action {
  text-align: right;
}

.fortune-link {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: underline;
}

/* 设置星座提示卡片 */
.setup-card {
  margin: var(--space-md);
  border: 2rpx dashed var(--primary-light);
  background: rgba(139, 92, 246, 0.05);
}

.setup-content {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.setup-icon {
  font-size: 64rpx;
  margin-right: var(--space-lg);
}

.setup-text {
  flex: 1;
}

.setup-title {
  display: block;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.setup-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.setup-action {
  text-align: center;
}

/* 快捷入口导航 */
.quick-nav {
  margin: var(--space-lg) var(--space-md) var(--space-md);
}

.nav-title {
  margin-bottom: var(--space-lg);
  text-align: center;
}

.title-text {
  display: block;
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.title-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.nav-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-md);
}

.nav-item {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-item:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-md);
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: var(--bg-gradient-purple);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-item:active::before {
  opacity: 1;
}

.nav-icon {
  font-size: 64rpx;
  margin-bottom: var(--space-md);
  display: block;
}

.nav-label {
  display: block;
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.nav-desc {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 今日推荐 */
.recommendation {
  margin: var(--space-lg) var(--space-md) var(--space-md);
}

.rec-title {
  margin-bottom: var(--space-lg);
}

.rec-list {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.rec-item {
  display: flex;
  align-items: center;
  padding: var(--space-lg);
  border-bottom: 1rpx solid var(--bg-secondary);
  transition: background-color 0.3s ease;
}

.rec-item:last-child {
  border-bottom: none;
}

.rec-item:active {
  background-color: rgba(139, 92, 246, 0.05);
}

.rec-icon {
  font-size: 48rpx;
  margin-right: var(--space-lg);
  flex-shrink: 0;
}

.rec-content {
  flex: 1;
}

.rec-name {
  display: block;
  font-size: var(--text-base);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.rec-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.rec-arrow {
  font-size: var(--text-lg);
  color: var(--text-light);
  margin-left: var(--space-md);
}

/* 响应式调整 */
@media (max-width: 375px) {
  .nav-grid {
    gap: var(--space-sm);
  }

  .nav-item {
    padding: var(--space-md);
  }

  .nav-icon {
    font-size: 56rpx;
  }
}
