<!--恋爱小程序首页-->
<navigation-bar title="恋爱小助手" back="{{false}}" extClass="gradient"></navigation-bar>

<scroll-view class="page-container" scroll-y type="list">
  <!-- 每日金句横幅 -->
  <view class="daily-quote-banner">
    <view class="quote-content">
      <text class="quote-icon">💕</text>
      <text class="quote-text">{{dailyQuote}}</text>
    </view>
    <view class="quote-date">{{currentDate}}</view>
  </view>

  <!-- 今日运势卡片 -->
  <view class="fortune-card card-gradient" wx:if="{{userHoroscope}}">
    <view class="fortune-header">
      <text class="fortune-title">{{userHoroscope.name}}今日运势</text>
      <text class="fortune-symbol">{{userHoroscope.symbol}}</text>
    </view>
    <view class="fortune-content">
      <text class="fortune-text">{{todayFortune}}</text>
    </view>
    <view class="fortune-action">
      <text class="fortune-link" bindtap="goToHoroscope">查看详细运势 ></text>
    </view>
  </view>

  <!-- 设置星座提示卡片 -->
  <view class="setup-card card" wx:else>
    <view class="setup-content">
      <text class="setup-icon">⭐</text>
      <view class="setup-text">
        <text class="setup-title">设置你的星座</text>
        <text class="setup-desc">获取专属的每日运势和恋爱建议</text>
      </view>
    </view>
    <view class="setup-action">
      <text class="btn btn-primary" bindtap="goToProfile">立即设置</text>
    </view>
  </view>

  <!-- 快捷入口导航 -->
  <view class="quick-nav">
    <view class="nav-title">
      <text class="title-text">探索更多</text>
      <text class="title-desc">发现爱情的奥秘</text>
    </view>

    <view class="nav-grid">
      <view class="nav-item" bindtap="goToTarot">
        <view class="nav-icon tarot-icon">🔮</view>
        <text class="nav-label">塔罗抽牌</text>
        <text class="nav-desc">探索内心真实想法</text>
      </view>

      <view class="nav-item" bindtap="goToTreehole">
        <view class="nav-icon tree-icon">🌳</view>
        <text class="nav-label">树洞广场</text>
        <text class="nav-desc">分享你的心事</text>
      </view>

      <view class="nav-item" bindtap="goToMatch">
        <view class="nav-icon match-icon">💝</view>
        <text class="nav-label">星座配对</text>
        <text class="nav-desc">测试你们的适配度</text>
      </view>

      <view class="nav-item" bindtap="goToAnalysis">
        <view class="nav-icon analysis-icon">📊</view>
        <text class="nav-label">性格分析</text>
        <text class="nav-desc">了解真实的自己</text>
      </view>
    </view>
  </view>

  <!-- 今日推荐 -->
  <view class="recommendation">
    <view class="rec-title">
      <text class="title-text">今日推荐</text>
    </view>

    <view class="rec-list">
      <view class="rec-item" wx:for="{{recommendations}}" wx:key="id" bindtap="goToRecommendation" data-item="{{item}}">
        <view class="rec-icon">{{item.icon}}</view>
        <view class="rec-content">
          <text class="rec-name">{{item.title}}</text>
          <text class="rec-desc">{{item.description}}</text>
        </view>
        <text class="rec-arrow">></text>
      </view>
    </view>
  </view>
</scroll-view>
