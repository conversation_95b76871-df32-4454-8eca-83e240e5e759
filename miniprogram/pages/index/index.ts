// 恋爱小程序首页
import { getTodayQuote, DateUtils } from '../../utils/date';
import { UserManager, STORAGE_KEYS, Storage } from '../../utils/storage';
import { getHoroscopeByDate, HOROSCOPE_SIGNS, HoroscopeSign } from '../../data/horoscope';

interface IndexData {
  dailyQuote: string;
  currentDate: string;
  userHoroscope: HoroscopeSign | null;
  todayFortune: string;
  recommendations: Array<{
    id: string;
    icon: string;
    title: string;
    description: string;
    type: string;
  }>;
}

Component<IndexData>({
  data: {
    dailyQuote: '',
    currentDate: '',
    userHoroscope: null,
    todayFortune: '',
    recommendations: []
  },

  lifetimes: {
    attached() {
      this.initPageData();
    }
  },

  pageLifetimes: {
    show() {
      // 每次显示页面时刷新数据
      this.loadUserHoroscope();
      this.loadTodayFortune();
    }
  },

  methods: {
    /**
     * 初始化页面数据
     */
    initPageData() {
      // 设置每日金句
      const quote = getTodayQuote();
      const today = new Date();
      const dateStr = `${today.getMonth() + 1}月${today.getDate()}日 ${DateUtils.getWeekday(today)}`;

      this.setData({
        dailyQuote: quote,
        currentDate: dateStr
      });

      // 加载用户星座信息
      this.loadUserHoroscope();

      // 加载今日运势
      this.loadTodayFortune();

      // 加载推荐内容
      this.loadRecommendations();
    },

    /**
     * 加载用户星座信息
     */
    loadUserHoroscope() {
      const userInfo = UserManager.getUserInfo();
      if (userInfo && userInfo.birthday) {
        const birthday = DateUtils.parseBirthday(userInfo.birthday);
        if (birthday) {
          const horoscope = getHoroscopeByDate(birthday.month, birthday.day);
          this.setData({
            userHoroscope: horoscope
          });
        }
      }
    },

    /**
     * 加载今日运势
     */
    loadTodayFortune() {
      const { userHoroscope } = this.data;
      if (userHoroscope) {
        // 这里可以从API获取真实的运势数据，现在使用模拟数据
        const fortunes = [
          '今日爱情运势不错，单身的你可能会遇到心仪的人，有伴的你们感情会更加甜蜜。',
          '今天适合表达内心的想法，勇敢地向喜欢的人表白吧！',
          '今日感情运势平稳，适合与伴侣深入交流，增进彼此了解。',
          '今天可能会有意外的浪漫惊喜，保持开放的心态去迎接。',
          '今日适合反思感情关系，思考如何让彼此的关系更加和谐。'
        ];

        const today = DateUtils.getToday();
        const savedFortune = Storage.get(`${STORAGE_KEYS.DAILY_FORTUNE}_${today}`);

        let fortune: string;
        if (savedFortune) {
          fortune = savedFortune;
        } else {
          // 根据日期生成固定的运势（确保同一天看到的运势相同）
          const dateHash = today.split('-').reduce((acc, val) => acc + parseInt(val), 0);
          fortune = fortunes[dateHash % fortunes.length];
          Storage.set(`${STORAGE_KEYS.DAILY_FORTUNE}_${today}`, fortune);
        }

        this.setData({
          todayFortune: fortune
        });
      }
    },

    /**
     * 加载推荐内容
     */
    loadRecommendations() {
      const recommendations = [
        {
          id: 'tarot_love',
          icon: '💕',
          title: '恋爱塔罗',
          description: '抽一张牌，看看你的爱情运势',
          type: 'tarot'
        },
        {
          id: 'tree_hot',
          icon: '🔥',
          title: '热门树洞',
          description: '看看大家都在聊什么',
          type: 'treehole'
        },
        {
          id: 'match_test',
          icon: '💖',
          title: '今日配对',
          description: '测试你和TA的星座配对指数',
          type: 'match'
        }
      ];

      this.setData({
        recommendations
      });
    },

    /**
     * 导航到星座运势页面
     */
    goToHoroscope() {
      wx.navigateTo({
        url: '/pages/horoscope/horoscope'
      });
    },

    /**
     * 导航到个人资料页面
     */
    goToProfile() {
      wx.navigateTo({
        url: '/pages/profile/profile'
      });
    },

    /**
     * 导航到塔罗页面
     */
    goToTarot() {
      wx.navigateTo({
        url: '/pages/tarot/tarot'
      });
    },

    /**
     * 导航到树洞页面
     */
    goToTreehole() {
      wx.navigateTo({
        url: '/pages/treehole/treehole'
      });
    },

    /**
     * 导航到星座配对页面
     */
    goToMatch() {
      wx.navigateTo({
        url: '/pages/horoscope/match/match'
      });
    },

    /**
     * 导航到性格分析页面
     */
    goToAnalysis() {
      wx.navigateTo({
        url: '/pages/horoscope/analysis/analysis'
      });
    },

    /**
     * 导航到推荐内容
     */
    goToRecommendation(e: WechatMiniprogram.TouchEvent) {
      const { item } = e.currentTarget.dataset;

      switch (item.type) {
        case 'tarot':
          this.goToTarot();
          break;
        case 'treehole':
          this.goToTreehole();
          break;
        case 'match':
          this.goToMatch();
          break;
        default:
          break;
      }
    }
  }
});
