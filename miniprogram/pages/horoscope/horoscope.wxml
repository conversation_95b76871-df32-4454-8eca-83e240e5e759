<!-- 星座运势页面 -->
<navigation-bar title="星座运势" back="{{true}}"></navigation-bar>

<scroll-view class="page-container" scroll-y type="list">
  <!-- 用户星座信息卡片 -->
  <view class="user-horoscope-card card-gradient" wx:if="{{userHoroscope}}">
    <view class="horoscope-header">
      <view class="horoscope-info">
        <text class="horoscope-symbol">{{userHoroscope.symbol}}</text>
        <view class="horoscope-text">
          <text class="horoscope-name">{{userHoroscope.name}}</text>
          <text class="horoscope-date">{{userHoroscope.dateRange}}</text>
        </view>
      </view>
      <text class="change-btn" bindtap="changeHoroscope">更换</text>
    </view>
    <view class="horoscope-traits">
      <text class="traits-title">性格特点</text>
      <view class="traits-list">
        <text class="trait-item" wx:for="{{userHoroscope.personality}}" wx:key="*this">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 设置星座提示 -->
  <view class="setup-horoscope card" wx:else>
    <view class="setup-content">
      <text class="setup-icon">⭐</text>
      <view class="setup-text">
        <text class="setup-title">设置你的星座</text>
        <text class="setup-desc">选择你的出生日期，获取专属运势</text>
      </view>
    </view>
    <view class="setup-action">
      <text class="btn btn-primary" bindtap="setupHoroscope">立即设置</text>
    </view>
  </view>

  <!-- 今日运势预览 -->
  <view class="today-fortune card" wx:if="{{userHoroscope}}">
    <view class="fortune-header">
      <text class="fortune-title">今日运势</text>
      <text class="fortune-date">{{todayDate}}</text>
    </view>
    <view class="fortune-content">
      <view class="fortune-item">
        <text class="fortune-label">综合运势</text>
        <view class="fortune-stars">
          <text class="star {{index < todayFortune.overall ? 'active' : ''}}" wx:for="{{5}}" wx:key="*this">★</text>
        </view>
      </view>
      <view class="fortune-item">
        <text class="fortune-label">爱情运势</text>
        <view class="fortune-stars">
          <text class="star {{index < todayFortune.love ? 'active' : ''}}" wx:for="{{5}}" wx:key="*this">★</text>
        </view>
      </view>
      <view class="fortune-item">
        <text class="fortune-label">事业运势</text>
        <view class="fortune-stars">
          <text class="star {{index < todayFortune.career ? 'active' : ''}}" wx:for="{{5}}" wx:key="*this">★</text>
        </view>
      </view>
    </view>
    <view class="fortune-action">
      <text class="view-detail-btn" bindtap="goToDaily">查看详细运势 ></text>
    </view>
  </view>

  <!-- 功能入口网格 -->
  <view class="function-grid">
    <view class="function-item" bindtap="goToDaily">
      <view class="function-icon daily-icon">📅</view>
      <text class="function-title">每日运势</text>
      <text class="function-desc">查看详细的每日星座运势</text>
    </view>

    <view class="function-item" bindtap="goToAnalysis">
      <view class="function-icon analysis-icon">🔍</view>
      <text class="function-title">性格分析</text>
      <text class="function-desc">深入了解星座性格特点</text>
    </view>

    <view class="function-item" bindtap="goToMatch">
      <view class="function-icon match-icon">💕</view>
      <text class="function-title">星座配对</text>
      <text class="function-desc">测试你们的星座适配度</text>
    </view>

    <view class="function-item" bindtap="goToWeekly">
      <view class="function-icon weekly-icon">📊</view>
      <text class="function-title">本周运势</text>
      <text class="function-desc">查看一周的运势趋势</text>
    </view>
  </view>

  <!-- 星座知识 -->
  <view class="horoscope-knowledge">
    <view class="knowledge-header">
      <text class="knowledge-title">星座小知识</text>
    </view>
    <view class="knowledge-list">
      <view class="knowledge-item" wx:for="{{knowledgeList}}" wx:key="id" bindtap="viewKnowledge" data-item="{{item}}">
        <text class="knowledge-icon">{{item.icon}}</text>
        <view class="knowledge-content">
          <text class="knowledge-name">{{item.title}}</text>
          <text class="knowledge-desc">{{item.description}}</text>
        </view>
        <text class="knowledge-arrow">></text>
      </view>
    </view>
  </view>
</scroll-view>
