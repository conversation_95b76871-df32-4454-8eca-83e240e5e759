// 星座运势页面
import { UserManager, STORAGE_KEYS, Storage } from '../../utils/storage';
import { getHoroscopeByDate, HOROSCOPE_SIGNS, HoroscopeSign } from '../../data/horoscope';
import { DateUtils } from '../../utils/date';

interface HoroscopeData {
  userHoroscope: HoroscopeSign | null;
  todayDate: string;
  todayFortune: {
    overall: number;
    love: number;
    career: number;
  };
  knowledgeList: Array<{
    id: string;
    icon: string;
    title: string;
    description: string;
  }>;
}

Component<HoroscopeData>({
  data: {
    userHoroscope: null,
    todayDate: '',
    todayFortune: {
      overall: 0,
      love: 0,
      career: 0
    },
    knowledgeList: []
  },

  lifetimes: {
    attached() {
      this.initPageData();
    }
  },

  pageLifetimes: {
    show() {
      this.loadUserHoroscope();
      this.loadTodayFortune();
    }
  },

  methods: {
    /**
     * 初始化页面数据
     */
    initPageData() {
      const today = new Date();
      const dateStr = `${today.getMonth() + 1}月${today.getDate()}日`;

      this.setData({
        todayDate: dateStr
      });

      this.loadUserHoroscope();
      this.loadTodayFortune();
      this.loadKnowledgeList();
    },

    /**
     * 加载用户星座信息
     */
    loadUserHoroscope() {
      const userInfo = UserManager.getUserInfo();
      if (userInfo && userInfo.birthday) {
        const birthday = DateUtils.parseBirthday(userInfo.birthday);
        if (birthday) {
          const horoscope = getHoroscopeByDate(birthday.month, birthday.day);
          this.setData({
            userHoroscope: horoscope
          });
        }
      }
    },

    /**
     * 加载今日运势
     */
    loadTodayFortune() {
      const { userHoroscope } = this.data;
      if (userHoroscope) {
        const today = DateUtils.getToday();
        const savedFortune = Storage.get(`horoscope_fortune_${today}`);

        let fortune;
        if (savedFortune) {
          fortune = savedFortune;
        } else {
          // 根据日期和星座生成运势评分（1-5星）
          const dateHash = today.split('-').reduce((acc, val) => acc + parseInt(val), 0);
          const signHash = userHoroscope.id.length;

          fortune = {
            overall: Math.max(1, (dateHash + signHash) % 5 + 1),
            love: Math.max(1, (dateHash * 2 + signHash) % 5 + 1),
            career: Math.max(1, (dateHash + signHash * 2) % 5 + 1)
          };

          Storage.set(`horoscope_fortune_${today}`, fortune);
        }

        this.setData({
          todayFortune: fortune
        });
      }
    },

    /**
     * 加载星座知识列表
     */
    loadKnowledgeList() {
      const knowledgeList = [
        {
          id: 'elements',
          icon: '🔥',
          title: '四大元素',
          description: '了解火土风水四大元素的特点'
        },
        {
          id: 'compatibility',
          icon: '💕',
          title: '星座配对',
          description: '探索不同星座之间的缘分'
        },
        {
          id: 'mythology',
          icon: '⭐',
          title: '星座神话',
          description: '聆听星座背后的古老传说'
        },
        {
          id: 'calendar',
          icon: '📅',
          title: '星座日历',
          description: '查看全年的星座运势趋势'
        }
      ];

      this.setData({
        knowledgeList
      });
    },

    /**
     * 设置星座
     */
    setupHoroscope() {
      wx.showModal({
        title: '设置星座',
        content: '请前往个人中心设置你的生日信息',
        confirmText: '去设置',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/profile/profile'
            });
          }
        }
      });
    },

    /**
     * 更换星座
     */
    changeHoroscope() {
      wx.navigateTo({
        url: '/pages/profile/profile'
      });
    },

    /**
     * 导航到每日运势
     */
    goToDaily() {
      if (!this.data.userHoroscope) {
        this.setupHoroscope();
        return;
      }
      wx.navigateTo({
        url: '/pages/horoscope/daily/daily'
      });
    },

    /**
     * 导航到性格分析
     */
    goToAnalysis() {
      if (!this.data.userHoroscope) {
        this.setupHoroscope();
        return;
      }
      wx.navigateTo({
        url: '/pages/horoscope/analysis/analysis'
      });
    },

    /**
     * 导航到星座配对
     */
    goToMatch() {
      wx.navigateTo({
        url: '/pages/horoscope/match/match'
      });
    },

    /**
     * 导航到本周运势
     */
    goToWeekly() {
      if (!this.data.userHoroscope) {
        this.setupHoroscope();
        return;
      }
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      });
    },

    /**
     * 查看星座知识
     */
    viewKnowledge(e: WechatMiniprogram.TouchEvent) {
      const { item } = e.currentTarget.dataset;
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      });
    }
  }
});
