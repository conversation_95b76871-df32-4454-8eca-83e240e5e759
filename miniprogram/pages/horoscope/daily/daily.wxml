<!-- 每日运势页面 -->
<navigation-bar title="每日运势" back="{{true}}"></navigation-bar>

<scroll-view class="page-container" scroll-y type="list">
  <!-- 星座信息头部 -->
  <view class="horoscope-header card-gradient" wx:if="{{userHoroscope}}">
    <view class="header-content">
      <text class="horoscope-symbol">{{userHoroscope.symbol}}</text>
      <view class="header-text">
        <text class="horoscope-name">{{userHoroscope.name}}</text>
        <text class="date-info">{{todayDate}} {{weekday}}</text>
      </view>
    </view>
  </view>

  <!-- 未设置星座提示 -->
  <view class="no-horoscope card" wx:else>
    <view class="no-horoscope-content">
      <text class="no-horoscope-icon">⭐</text>
      <text class="no-horoscope-title">请先设置你的星座</text>
      <text class="no-horoscope-desc">设置生日信息后即可查看专属运势</text>
    </view>
    <view class="no-horoscope-action">
      <text class="btn btn-primary" bindtap="goToProfile">去设置</text>
    </view>
  </view>

  <!-- 运势详情 -->
  <view class="fortune-details" wx:if="{{userHoroscope}}">
    <!-- 综合运势 -->
    <view class="fortune-section card">
      <view class="section-header">
        <text class="section-title">综合运势</text>
        <view class="fortune-stars">
          <text class="star {{index < todayFortune.overall ? 'active' : ''}}" wx:for="{{5}}" wx:key="*this">★</text>
        </view>
      </view>
      <text class="fortune-text">{{fortuneTexts.overall}}</text>
    </view>

    <!-- 爱情运势 -->
    <view class="fortune-section card love-section">
      <view class="section-header">
        <text class="section-title">💕 爱情运势</text>
        <view class="fortune-stars">
          <text class="star {{index < todayFortune.love ? 'active' : ''}}" wx:for="{{5}}" wx:key="*this">★</text>
        </view>
      </view>
      <text class="fortune-text">{{fortuneTexts.love}}</text>
      <view class="love-advice" wx:if="{{loveAdvice}}">
        <text class="advice-title">💡 恋爱建议</text>
        <text class="advice-text">{{loveAdvice}}</text>
      </view>
    </view>

    <!-- 事业运势 -->
    <view class="fortune-section card career-section">
      <view class="section-header">
        <text class="section-title">💼 事业运势</text>
        <view class="fortune-stars">
          <text class="star {{index < todayFortune.career ? 'active' : ''}}" wx:for="{{5}}" wx:key="*this">★</text>
        </view>
      </view>
      <text class="fortune-text">{{fortuneTexts.career}}</text>
    </view>

    <!-- 幸运元素 -->
    <view class="lucky-elements card">
      <view class="elements-header">
        <text class="elements-title">🍀 今日幸运</text>
      </view>
      <view class="elements-grid">
        <view class="element-item">
          <text class="element-label">幸运数字</text>
          <text class="element-value">{{luckyElements.number}}</text>
        </view>
        <view class="element-item">
          <text class="element-label">幸运颜色</text>
          <text class="element-value">{{luckyElements.color}}</text>
        </view>
        <view class="element-item">
          <text class="element-label">幸运方位</text>
          <text class="element-value">{{luckyElements.direction}}</text>
        </view>
        <view class="element-item">
          <text class="element-label">开运食物</text>
          <text class="element-value">{{luckyElements.food}}</text>
        </view>
      </view>
    </view>

    <!-- 今日建议 -->
    <view class="daily-suggestions card">
      <view class="suggestions-header">
        <text class="suggestions-title">📝 今日建议</text>
      </view>
      <view class="suggestions-list">
        <view class="suggestion-item" wx:for="{{dailySuggestions}}" wx:key="*this">
          <text class="suggestion-icon">•</text>
          <text class="suggestion-text">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 分享按钮 -->
  <view class="share-section" wx:if="{{userHoroscope}}">
    <button class="share-btn btn btn-outline" open-type="share">
      <text class="share-icon">📤</text>
      <text class="share-text">分享今日运势</text>
    </button>
  </view>
</scroll-view>
