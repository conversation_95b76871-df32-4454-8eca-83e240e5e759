/* 每日运势页面样式 */

/* 星座信息头部 */
.horoscope-header {
  margin: var(--space-md);
  position: relative;
  overflow: hidden;
}

.header-content {
  display: flex;
  align-items: center;
}

.horoscope-symbol {
  font-size: 100rpx;
  margin-right: var(--space-lg);
}

.header-text {
  flex: 1;
}

.horoscope-name {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--text-white);
  margin-bottom: var(--space-xs);
}

.date-info {
  font-size: var(--text-base);
  color: rgba(255, 255, 255, 0.9);
}

/* 未设置星座提示 */
.no-horoscope {
  margin: var(--space-md);
  text-align: center;
  border: 2rpx dashed var(--primary-light);
  background: rgba(139, 92, 246, 0.05);
}

.no-horoscope-content {
  margin-bottom: var(--space-lg);
}

.no-horoscope-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: var(--space-lg);
}

.no-horoscope-title {
  display: block;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.no-horoscope-desc {
  font-size: var(--text-base);
  color: var(--text-secondary);
}

.no-horoscope-action {
  text-align: center;
}

/* 运势详情 */
.fortune-details {
  padding: 0 var(--space-md);
}

.fortune-section {
  margin-bottom: var(--space-md);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
}

.section-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.fortune-stars {
  display: flex;
  gap: var(--space-xs);
}

.star {
  font-size: var(--text-lg);
  color: var(--accent-light);
  transition: color 0.3s ease;
}

.star.active {
  color: var(--gold-color);
}

.fortune-text {
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

/* 爱情运势特殊样式 */
.love-section {
  border-left: 6rpx solid var(--secondary-color);
}

.love-advice {
  background: rgba(248, 187, 217, 0.1);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  margin-top: var(--space-md);
}

.advice-title {
  display: block;
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--secondary-dark);
  margin-bottom: var(--space-sm);
}

.advice-text {
  font-size: var(--text-sm);
  line-height: 1.5;
  color: var(--text-primary);
}

/* 事业运势特殊样式 */
.career-section {
  border-left: 6rpx solid var(--accent-color);
}

/* 幸运元素 */
.lucky-elements {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border: 1rpx solid rgba(245, 158, 11, 0.2);
}

.elements-header {
  margin-bottom: var(--space-lg);
}

.elements-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.elements-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-md);
}

.element-item {
  text-align: center;
  padding: var(--space-md);
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.element-label {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
}

.element-value {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
}

/* 今日建议 */
.daily-suggestions {
  background: rgba(139, 92, 246, 0.05);
  border: 1rpx solid rgba(139, 92, 246, 0.2);
}

.suggestions-header {
  margin-bottom: var(--space-lg);
}

.suggestions-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
}

.suggestion-icon {
  font-size: var(--text-lg);
  color: var(--primary-color);
  margin-right: var(--space-md);
  margin-top: 4rpx;
  flex-shrink: 0;
}

.suggestion-text {
  font-size: var(--text-base);
  line-height: 1.5;
  color: var(--text-primary);
  flex: 1;
}

/* 分享按钮 */
.share-section {
  padding: var(--space-lg) var(--space-md);
  text-align: center;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: var(--space-lg);
  border: 2rpx solid var(--primary-color);
  background: transparent;
  color: var(--primary-color);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  transition: all 0.3s ease;
}

.share-btn:active {
  background: rgba(139, 92, 246, 0.1);
  transform: scale(0.98);
}

.share-icon {
  font-size: var(--text-lg);
  margin-right: var(--space-sm);
}

.share-text {
  font-weight: 500;
}
