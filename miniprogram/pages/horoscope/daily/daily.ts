// 每日运势页面
import { UserManager, STORAGE_KEYS, Storage } from '../../../utils/storage';
import { getHoroscopeByDate, HOROSCOPE_SIGNS, HoroscopeSign } from '../../../data/horoscope';
import { DateUtils } from '../../../utils/date';

interface DailyData {
  userHoroscope: HoroscopeSign | null;
  todayDate: string;
  weekday: string;
  todayFortune: {
    overall: number;
    love: number;
    career: number;
  };
  fortuneTexts: {
    overall: string;
    love: string;
    career: string;
  };
  loveAdvice: string;
  luckyElements: {
    number: string;
    color: string;
    direction: string;
    food: string;
  };
  dailySuggestions: string[];
}

Component<DailyData>({
  data: {
    userHoroscope: null,
    todayDate: '',
    weekday: '',
    todayFortune: {
      overall: 0,
      love: 0,
      career: 0
    },
    fortuneTexts: {
      overall: '',
      love: '',
      career: ''
    },
    loveAdvice: '',
    luckyElements: {
      number: '',
      color: '',
      direction: '',
      food: ''
    },
    dailySuggestions: []
  },

  lifetimes: {
    attached() {
      this.initPageData();
    }
  },

  methods: {
    /**
     * 初始化页面数据
     */
    initPageData() {
      const today = new Date();
      const dateStr = `${today.getMonth() + 1}月${today.getDate()}日`;
      const weekday = DateUtils.getWeekday(today);

      this.setData({
        todayDate: dateStr,
        weekday: weekday
      });

      this.loadUserHoroscope();
    },

    /**
     * 加载用户星座信息
     */
    loadUserHoroscope() {
      const userInfo = UserManager.getUserInfo();
      if (userInfo && userInfo.birthday) {
        const birthday = DateUtils.parseBirthday(userInfo.birthday);
        if (birthday) {
          const horoscope = getHoroscopeByDate(birthday.month, birthday.day);
          this.setData({
            userHoroscope: horoscope
          });

          if (horoscope) {
            this.loadDailyFortune(horoscope);
          }
        }
      }
    },

    /**
     * 加载每日运势详情
     */
    loadDailyFortune(horoscope: HoroscopeSign) {
      const today = DateUtils.getToday();
      const cacheKey = `daily_fortune_${horoscope.id}_${today}`;
      const savedData = Storage.get(cacheKey);

      if (savedData) {
        this.setData(savedData);
        return;
      }

      // 生成运势数据
      const dateHash = today.split('-').reduce((acc, val) => acc + parseInt(val), 0);
      const signHash = horoscope.id.length;

      // 运势评分（1-5星）
      const fortune = {
        overall: Math.max(1, (dateHash + signHash) % 5 + 1),
        love: Math.max(1, (dateHash * 2 + signHash) % 5 + 1),
        career: Math.max(1, (dateHash + signHash * 2) % 5 + 1)
      };

      // 运势文本
      const fortuneTexts = this.generateFortuneTexts(horoscope, fortune);

      // 恋爱建议
      const loveAdvice = this.generateLoveAdvice(horoscope, fortune.love);

      // 幸运元素
      const luckyElements = this.generateLuckyElements(dateHash, signHash);

      // 今日建议
      const dailySuggestions = this.generateDailySuggestions(horoscope, fortune);

      const data = {
        todayFortune: fortune,
        fortuneTexts,
        loveAdvice,
        luckyElements,
        dailySuggestions
      };

      // 缓存数据
      Storage.set(cacheKey, data);

      this.setData(data);
    },

    /**
     * 生成运势文本
     */
    generateFortuneTexts(horoscope: HoroscopeSign, fortune: any) {
      const overallTexts = [
        '今日整体运势平稳，适合按部就班地处理各种事务。',
        '今天是充满活力的一天，你的积极态度会带来好运。',
        '今日运势不错，有机会在某个领域取得突破。',
        '今天可能会遇到一些小挑战，但都能顺利解决。',
        '今日运势极佳，是实现目标的绝佳时机！'
      ];

      const loveTexts = [
        '感情方面需要更多耐心，避免因小事产生争执。',
        '今日桃花运不错，单身的你可能会遇到心仪的人。',
        '与伴侣的关系和谐，适合深入交流增进感情。',
        '今天适合表达内心想法，勇敢地说出你的爱意。',
        '爱情运势极佳，可能会有浪漫的惊喜等着你！'
      ];

      const careerTexts = [
        '工作中保持专注，避免被琐事分散注意力。',
        '今日工作效率较高，适合处理重要的项目。',
        '可能会有新的合作机会出现，要把握住。',
        '今天适合展示你的才能，会得到认可。',
        '事业运势极佳，有望在工作中取得重大进展！'
      ];

      return {
        overall: overallTexts[fortune.overall - 1],
        love: loveTexts[fortune.love - 1],
        career: careerTexts[fortune.career - 1]
      };
    },

    /**
     * 生成恋爱建议
     */
    generateLoveAdvice(horoscope: HoroscopeSign, loveScore: number) {
      const advices = [
        '今天在感情中要多一些包容和理解，避免过于固执己见。',
        '适合与心仪的人进行轻松愉快的交流，展现你的幽默感。',
        '今日适合为感情付出行动，一个小小的惊喜会很有效果。',
        '保持真诚的态度，诚实地表达你的感受和想法。',
        '今天是表白的好日子，勇敢地向喜欢的人表达爱意吧！'
      ];

      return advices[loveScore - 1];
    },

    /**
     * 生成幸运元素
     */
    generateLuckyElements(dateHash: number, signHash: number) {
      const numbers = ['3', '7', '9', '12', '21', '28'];
      const colors = ['紫色', '粉色', '蓝色', '金色', '白色', '绿色'];
      const directions = ['东方', '南方', '西方', '北方', '东南', '西北'];
      const foods = ['草莓', '巧克力', '蜂蜜', '坚果', '酸奶', '水果'];

      return {
        number: numbers[dateHash % numbers.length],
        color: colors[signHash % colors.length],
        direction: directions[(dateHash + signHash) % directions.length],
        food: foods[(dateHash * 2) % foods.length]
      };
    },

    /**
     * 生成今日建议
     */
    generateDailySuggestions(horoscope: HoroscopeSign, fortune: any) {
      const suggestions = [
        '保持积极乐观的心态，用微笑面对每一个人',
        '今天适合学习新知识或培养新的兴趣爱好',
        '多与朋友交流，分享彼此的想法和感受',
        '注意身体健康，适当运动和休息',
        '对待感情要真诚，用心经营每一段关系'
      ];

      // 根据运势调整建议
      if (fortune.love >= 4) {
        suggestions.push('今天是表达爱意的好时机，不要错过');
      }

      if (fortune.career >= 4) {
        suggestions.push('工作中要积极主动，展现你的能力');
      }

      return suggestions.slice(0, 4); // 返回4条建议
    },

    /**
     * 前往个人中心设置
     */
    goToProfile() {
      wx.navigateTo({
        url: '/pages/profile/profile'
      });
    },

    /**
     * 分享功能
     */
    onShareAppMessage() {
      const { userHoroscope, todayDate } = this.data;
      return {
        title: `${userHoroscope?.name}${todayDate}运势`,
        path: '/pages/horoscope/daily/daily',
        imageUrl: '' // 可以设置分享图片
      };
    }
  }
});
