/* 性格分析页面样式 */

/* 星座信息头部 */
.horoscope-header {
  margin: var(--space-md);
  position: relative;
  overflow: hidden;
}

.header-content {
  display: flex;
  align-items: center;
}

.horoscope-symbol {
  font-size: 100rpx;
  margin-right: var(--space-lg);
}

.header-text {
  flex: 1;
}

.horoscope-name {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--text-white);
  margin-bottom: var(--space-xs);
}

.horoscope-element {
  display: block;
  font-size: var(--text-base);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-xs);
}

.horoscope-date {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.8);
}

/* 未设置星座提示 */
.no-horoscope {
  margin: var(--space-md);
  text-align: center;
  border: 2rpx dashed var(--primary-light);
  background: rgba(139, 92, 246, 0.05);
}

.no-horoscope-content {
  margin-bottom: var(--space-lg);
}

.no-horoscope-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: var(--space-lg);
}

.no-horoscope-title {
  display: block;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.no-horoscope-desc {
  font-size: var(--text-base);
  color: var(--text-secondary);
}

.no-horoscope-action {
  text-align: center;
}

/* 分析内容 */
.analysis-content {
  padding: 0 var(--space-md);
}

.section-header {
  margin-bottom: var(--space-lg);
}

.section-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

/* 核心性格特点 */
.personality-section {
  margin-bottom: var(--space-md);
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(167, 139, 250, 0.1) 100%);
  border: 1rpx solid rgba(139, 92, 246, 0.2);
}

.personality-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-md);
}

.personality-item {
  background: rgba(255, 255, 255, 0.8);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  text-align: center;
  box-shadow: var(--shadow-sm);
}

.personality-text {
  font-size: var(--text-base);
  font-weight: 500;
  color: var(--text-primary);
}

/* 恋爱特质 */
.love-traits-section {
  margin-bottom: var(--space-md);
  background: linear-gradient(135deg, rgba(248, 187, 217, 0.1) 0%, rgba(252, 231, 243, 0.1) 100%);
  border: 1rpx solid rgba(248, 187, 217, 0.3);
}

.traits-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.trait-item {
  display: flex;
  align-items: flex-start;
}

.trait-icon {
  font-size: var(--text-lg);
  color: var(--secondary-color);
  margin-right: var(--space-md);
  margin-top: 4rpx;
  flex-shrink: 0;
}

.trait-text {
  font-size: var(--text-base);
  line-height: 1.5;
  color: var(--text-primary);
  flex: 1;
}

/* 优势与挑战 */
.strengths-challenges {
  margin-bottom: var(--space-md);
}

.sc-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-md);
}

.sc-item {
  padding: var(--space-lg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.sc-item.strengths {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(74, 222, 128, 0.1) 100%);
  border: 1rpx solid rgba(34, 197, 94, 0.2);
}

.sc-item.challenges {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);
  border: 1rpx solid rgba(245, 158, 11, 0.2);
}

.sc-label {
  display: block;
  font-size: var(--text-base);
  font-weight: 600;
  margin-bottom: var(--space-md);
}

.sc-item.strengths .sc-label {
  color: #059669;
}

.sc-item.challenges .sc-label {
  color: #D97706;
}

.sc-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.sc-text {
  font-size: var(--text-sm);
  line-height: 1.4;
  color: var(--text-primary);
  padding: var(--space-xs) var(--space-sm);
  background: rgba(255, 255, 255, 0.6);
  border-radius: var(--radius-sm);
}

/* 最佳配对 */
.compatibility-section {
  margin-bottom: var(--space-md);
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.1) 0%, rgba(251, 113, 133, 0.1) 100%);
  border: 1rpx solid rgba(236, 72, 153, 0.2);
}

.compatibility-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.compatibility-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md);
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.compatibility-item:active {
  background: rgba(236, 72, 153, 0.1);
  transform: scale(0.98);
}

.compatibility-name {
  font-size: var(--text-base);
  font-weight: 500;
  color: var(--text-primary);
}

.compatibility-arrow {
  font-size: var(--text-lg);
  color: var(--text-light);
}

/* 性格建议 */
.advice-section {
  margin-bottom: var(--space-md);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(129, 140, 248, 0.1) 100%);
  border: 1rpx solid rgba(99, 102, 241, 0.2);
}

.advice-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.advice-item {
  display: flex;
  align-items: flex-start;
}

.advice-icon {
  font-size: var(--text-lg);
  margin-right: var(--space-md);
  margin-top: 4rpx;
  flex-shrink: 0;
}

.advice-text {
  font-size: var(--text-base);
  line-height: 1.5;
  color: var(--text-primary);
  flex: 1;
}

/* 元素特征 */
.element-section {
  margin-bottom: var(--space-md);
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);
  border: 1rpx solid rgba(245, 158, 11, 0.2);
}

.element-description {
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
}

.element-traits {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.element-trait {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md);
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.trait-label {
  font-size: var(--text-base);
  color: var(--text-secondary);
}

.trait-value {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
}

/* 分享按钮 */
.share-section {
  padding: var(--space-lg) var(--space-md);
  text-align: center;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: var(--space-lg);
  border: 2rpx solid var(--primary-color);
  background: transparent;
  color: var(--primary-color);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  transition: all 0.3s ease;
}

.share-btn:active {
  background: rgba(139, 92, 246, 0.1);
  transform: scale(0.98);
}

.share-icon {
  font-size: var(--text-lg);
  margin-right: var(--space-sm);
}

.share-text {
  font-weight: 500;
}
