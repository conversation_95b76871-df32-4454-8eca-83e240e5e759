<!-- 性格分析页面 -->
<navigation-bar title="性格分析" back="{{true}}"></navigation-bar>

<scroll-view class="page-container" scroll-y type="list">
  <!-- 星座信息头部 -->
  <view class="horoscope-header card-gradient" wx:if="{{userHoroscope}}">
    <view class="header-content">
      <text class="horoscope-symbol">{{userHoroscope.symbol}}</text>
      <view class="header-text">
        <text class="horoscope-name">{{userHoroscope.name}}</text>
        <text class="horoscope-element">{{userHoroscope.element}}象星座</text>
        <text class="horoscope-date">{{userHoroscope.dateRange}}</text>
      </view>
    </view>
  </view>

  <!-- 未设置星座提示 -->
  <view class="no-horoscope card" wx:else>
    <view class="no-horoscope-content">
      <text class="no-horoscope-icon">⭐</text>
      <text class="no-horoscope-title">请先设置你的星座</text>
      <text class="no-horoscope-desc">设置生日信息后即可查看性格分析</text>
    </view>
    <view class="no-horoscope-action">
      <text class="btn btn-primary" bindtap="goToProfile">去设置</text>
    </view>
  </view>

  <!-- 性格分析内容 -->
  <view class="analysis-content" wx:if="{{userHoroscope}}">
    <!-- 核心性格特点 -->
    <view class="personality-section card">
      <view class="section-header">
        <text class="section-title">🎭 核心性格</text>
      </view>
      <view class="personality-grid">
        <view class="personality-item" wx:for="{{userHoroscope.personality}}" wx:key="*this">
          <text class="personality-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 恋爱特质 -->
    <view class="love-traits-section card">
      <view class="section-header">
        <text class="section-title">💕 恋爱特质</text>
      </view>
      <view class="traits-list">
        <view class="trait-item" wx:for="{{userHoroscope.loveTraits}}" wx:key="*this">
          <text class="trait-icon">•</text>
          <text class="trait-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 优势与挑战 -->
    <view class="strengths-challenges card">
      <view class="section-header">
        <text class="section-title">⚖️ 优势与挑战</text>
      </view>
      <view class="sc-grid">
        <view class="sc-item strengths">
          <text class="sc-label">✨ 优势</text>
          <view class="sc-list">
            <text class="sc-text" wx:for="{{analysisData.strengths}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
        <view class="sc-item challenges">
          <text class="sc-label">⚠️ 挑战</text>
          <view class="sc-list">
            <text class="sc-text" wx:for="{{analysisData.challenges}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 最佳配对 -->
    <view class="compatibility-section card">
      <view class="section-header">
        <text class="section-title">💖 最佳配对</text>
      </view>
      <view class="compatibility-grid">
        <view class="compatibility-item" wx:for="{{userHoroscope.compatibility}}" wx:key="*this" bindtap="viewCompatibility" data-sign="{{item}}">
          <text class="compatibility-name">{{item}}</text>
          <text class="compatibility-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 性格建议 -->
    <view class="advice-section card">
      <view class="section-header">
        <text class="section-title">💡 性格建议</text>
      </view>
      <view class="advice-list">
        <view class="advice-item" wx:for="{{analysisData.advice}}" wx:key="*this">
          <text class="advice-icon">📝</text>
          <text class="advice-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 元素特征 -->
    <view class="element-section card">
      <view class="section-header">
        <text class="section-title">🔥 {{userHoroscope.element}}象特征</text>
      </view>
      <text class="element-description">{{elementDescription}}</text>
      <view class="element-traits">
        <view class="element-trait" wx:for="{{elementTraits}}" wx:key="*this">
          <text class="trait-label">{{item.label}}</text>
          <text class="trait-value">{{item.value}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 分享按钮 -->
  <view class="share-section" wx:if="{{userHoroscope}}">
    <button class="share-btn btn btn-outline" open-type="share">
      <text class="share-icon">📤</text>
      <text class="share-text">分享我的性格分析</text>
    </button>
  </view>
</scroll-view>
