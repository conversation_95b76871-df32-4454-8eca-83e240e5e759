// 性格分析页面
import { UserManager } from '../../../utils/storage';
import { getHoroscopeByDate, HOROSCOPE_SIGNS, HoroscopeSign } from '../../../data/horoscope';
import { DateUtils } from '../../../utils/date';

interface AnalysisData {
  userHoroscope: HoroscopeSign | null;
  analysisData: {
    strengths: string[];
    challenges: string[];
    advice: string[];
  };
  elementDescription: string;
  elementTraits: Array<{
    label: string;
    value: string;
  }>;
}

Component<AnalysisData>({
  data: {
    userHoroscope: null,
    analysisData: {
      strengths: [],
      challenges: [],
      advice: []
    },
    elementDescription: '',
    elementTraits: []
  },

  lifetimes: {
    attached() {
      this.initPageData();
    }
  },

  methods: {
    /**
     * 初始化页面数据
     */
    initPageData() {
      this.loadUserHoroscope();
    },

    /**
     * 加载用户星座信息
     */
    loadUserHoroscope() {
      const userInfo = UserManager.getUserInfo();
      if (userInfo && userInfo.birthday) {
        const birthday = DateUtils.parseBirthday(userInfo.birthday);
        if (birthday) {
          const horoscope = getHoroscopeByDate(birthday.month, birthday.day);
          this.setData({
            userHoroscope: horoscope
          });

          if (horoscope) {
            this.loadAnalysisData(horoscope);
          }
        }
      }
    },

    /**
     * 加载分析数据
     */
    loadAnalysisData(horoscope: HoroscopeSign) {
      const analysisData = this.generateAnalysisData(horoscope);
      const elementDescription = this.getElementDescription(horoscope.element);
      const elementTraits = this.getElementTraits(horoscope.element);

      this.setData({
        analysisData,
        elementDescription,
        elementTraits
      });
    },

    /**
     * 生成分析数据
     */
    generateAnalysisData(horoscope: HoroscopeSign) {
      const analysisMap: Record<string, any> = {
        'aries': {
          strengths: ['充满活力', '勇敢无畏', '领导能力强', '行动力强'],
          challenges: ['容易冲动', '缺乏耐心', '有时过于自我', '情绪波动大'],
          advice: ['学会控制情绪，三思而后行', '培养耐心，倾听他人意见', '在感情中给对方更多空间', '将冲动转化为积极的行动力']
        },
        'taurus': {
          strengths: ['稳重可靠', '有责任感', '忠诚专一', '审美能力强'],
          challenges: ['过于固执', '变化适应慢', '有时过于保守', '占有欲较强'],
          advice: ['尝试接受新事物和变化', '在感情中给予更多信任', '学会适时妥协和沟通', '保持开放的心态面对未知']
        },
        'gemini': {
          strengths: ['聪明机智', '适应力强', '沟通能力佳', '思维活跃'],
          challenges: ['注意力分散', '缺乏持久力', '有时表里不一', '决策困难'],
          advice: ['培养专注力，坚持完成目标', '在感情中保持真诚一致', '学会深度思考而非浅尝辄止', '给自己时间做重要决定']
        },
        'cancer': {
          strengths: ['情感丰富', '直觉敏锐', '善于照顾人', '家庭观念强'],
          challenges: ['过于敏感', '情绪化严重', '缺乏安全感', '容易多疑'],
          advice: ['学会管理情绪，保持理性', '建立自信，相信自己的价值', '在感情中多沟通少猜疑', '培养独立性和安全感']
        },
        'leo': {
          strengths: ['自信大方', '慷慨热情', '创造力强', '领导魅力'],
          challenges: ['过于自负', '需要关注', '有时霸道', '面子观念重'],
          advice: ['学会谦逊，倾听他人意见', '在感情中给对方表现机会', '放下面子，真诚道歉和沟通', '将自信转化为对他人的鼓励']
        },
        'virgo': {
          strengths: ['细心谨慎', '完美主义', '分析能力强', '服务精神'],
          challenges: ['过于挑剔', '焦虑倾向', '缺乏自信', '过度分析'],
          advice: ['学会接受不完美，降低标准', '培养自信，相信自己的能力', '在感情中多包容少批评', '适时放松，享受当下']
        },
        'libra': {
          strengths: ['优雅和谐', '公正客观', '社交能力强', '审美品味佳'],
          challenges: ['犹豫不决', '避免冲突', '依赖性强', '表面化倾向'],
          advice: ['培养决断力，勇于做选择', '学会面对和解决冲突', '在感情中保持独立性', '追求真实而非表面和谐']
        },
        'scorpio': {
          strengths: ['意志坚强', '洞察力强', '专注深入', '忠诚可靠'],
          challenges: ['占有欲强', '报复心重', '过于神秘', '情绪极端'],
          advice: ['学会宽恕，放下过去', '在感情中给予信任和空间', '适度分享内心想法', '控制极端情绪，保持平衡']
        },
        'sagittarius': {
          strengths: ['乐观开朗', '自由独立', '哲学思维', '冒险精神'],
          challenges: ['缺乏承诺', '过于直率', '注意力分散', '逃避责任'],
          advice: ['学会承担责任和承诺', '在表达时考虑他人感受', '培养专注力和持久力', '在自由和责任间找到平衡']
        },
        'capricorn': {
          strengths: ['目标明确', '有责任心', '务实稳重', '持久力强'],
          challenges: ['过于严肃', '缺乏灵活性', '工作狂倾向', '情感表达困难'],
          advice: ['学会放松和享受生活', '在感情中多表达关爱', '保持工作生活平衡', '培养幽默感和灵活性']
        },
        'aquarius': {
          strengths: ['独立创新', '理性客观', '人道主义', '思维前卫'],
          challenges: ['情感疏离', '过于理性', '固执己见', '缺乏耐心'],
          advice: ['学会表达和接受情感', '在理性中加入感性元素', '倾听他人观点，保持开放', '在感情中投入更多时间和精力']
        },
        'pisces': {
          strengths: ['富有同情心', '想象力丰富', '直觉敏锐', '艺术天赋'],
          challenges: ['过于理想化', '逃避现实', '情绪波动', '缺乏界限'],
          advice: ['学会面对现实，脚踏实地', '建立健康的情感界限', '将想象力转化为实际行动', '在感情中保持理性判断']
        }
      };

      return analysisMap[horoscope.id] || {
        strengths: ['独特的个性', '特殊的魅力'],
        challenges: ['需要更多了解'],
        advice: ['继续探索自己的特质']
      };
    },

    /**
     * 获取元素描述
     */
    getElementDescription(element: string): string {
      const descriptions: Record<string, string> = {
        '火': '火象星座充满活力和热情，具有强烈的行动力和领导欲望。你们天生具有开拓精神，勇于面对挑战，但有时也容易冲动和急躁。',
        '土': '土象星座务实稳重，注重现实和物质基础。你们有很强的责任感和持久力，善于规划和执行，但有时可能过于保守和固执。',
        '风': '风象星座聪明机智，善于沟通和思考。你们适应力强，思维活跃，喜欢新鲜事物，但有时可能缺乏持久力和深度。',
        '水': '水象星座情感丰富，直觉敏锐。你们富有同情心和想象力，善于理解他人，但有时可能过于敏感和情绪化。'
      };

      return descriptions[element] || '每个星座都有其独特的特质和魅力。';
    },

    /**
     * 获取元素特质
     */
    getElementTraits(element: string) {
      const traits: Record<string, Array<{label: string, value: string}>> = {
        '火': [
          { label: '能量水平', value: '很高' },
          { label: '行动力', value: '极强' },
          { label: '领导能力', value: '天生' },
          { label: '冒险精神', value: '充足' }
        ],
        '土': [
          { label: '稳定性', value: '极高' },
          { label: '实用性', value: '很强' },
          { label: '责任感', value: '深重' },
          { label: '持久力', value: '出色' }
        ],
        '风': [
          { label: '沟通能力', value: '卓越' },
          { label: '适应性', value: '很强' },
          { label: '思维活跃度', value: '极高' },
          { label: '社交能力', value: '出众' }
        ],
        '水': [
          { label: '情感深度', value: '很深' },
          { label: '直觉力', value: '敏锐' },
          { label: '同理心', value: '强烈' },
          { label: '想象力', value: '丰富' }
        ]
      };

      return traits[element] || [];
    },

    /**
     * 查看星座配对详情
     */
    viewCompatibility(e: WechatMiniprogram.TouchEvent) {
      const { sign } = e.currentTarget.dataset;
      wx.navigateTo({
        url: `/pages/horoscope/match/match?targetSign=${sign}`
      });
    },

    /**
     * 前往个人中心设置
     */
    goToProfile() {
      wx.navigateTo({
        url: '/pages/profile/profile'
      });
    },

    /**
     * 分享功能
     */
    onShareAppMessage() {
      const { userHoroscope } = this.data;
      return {
        title: `${userHoroscope?.name}性格分析`,
        path: '/pages/horoscope/analysis/analysis',
        imageUrl: '' // 可以设置分享图片
      };
    }
  }
});
