/* 星座运势页面样式 */

/* 用户星座信息卡片 */
.user-horoscope-card {
  margin: var(--space-md);
  position: relative;
  overflow: hidden;
}

.horoscope-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
}

.horoscope-info {
  display: flex;
  align-items: center;
}

.horoscope-symbol {
  font-size: 80rpx;
  margin-right: var(--space-lg);
}

.horoscope-text {
  display: flex;
  flex-direction: column;
}

.horoscope-name {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-white);
  margin-bottom: var(--space-xs);
}

.horoscope-date {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.8);
}

.change-btn {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.9);
  padding: var(--space-sm) var(--space-md);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.1);
}

.horoscope-traits {
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  padding-top: var(--space-lg);
}

.traits-title {
  font-size: var(--text-base);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-md);
  display: block;
}

.traits-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
}

.trait-item {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-white);
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
}

/* 设置星座提示 */
.setup-horoscope {
  margin: var(--space-md);
  border: 2rpx dashed var(--primary-light);
  background: rgba(139, 92, 246, 0.05);
}

.setup-content {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.setup-icon {
  font-size: 64rpx;
  margin-right: var(--space-lg);
}

.setup-text {
  flex: 1;
}

.setup-title {
  display: block;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.setup-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.setup-action {
  text-align: center;
}

/* 今日运势预览 */
.today-fortune {
  margin: var(--space-md);
}

.fortune-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
}

.fortune-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.fortune-date {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.fortune-content {
  margin-bottom: var(--space-lg);
}

.fortune-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
}

.fortune-item:last-child {
  margin-bottom: 0;
}

.fortune-label {
  font-size: var(--text-base);
  color: var(--text-primary);
}

.fortune-stars {
  display: flex;
  gap: var(--space-xs);
}

.star {
  font-size: var(--text-lg);
  color: var(--accent-light);
  transition: color 0.3s ease;
}

.star.active {
  color: var(--gold-color);
}

.fortune-action {
  text-align: right;
}

.view-detail-btn {
  font-size: var(--text-sm);
  color: var(--primary-color);
  text-decoration: underline;
}

/* 功能入口网格 */
.function-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-md);
  margin: var(--space-lg) var(--space-md);
}

.function-item {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.function-item:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-md);
}

.function-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: var(--bg-gradient-purple);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.function-item:active::before {
  opacity: 1;
}

.function-icon {
  font-size: 64rpx;
  margin-bottom: var(--space-md);
  display: block;
}

.function-title {
  display: block;
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.function-desc {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 星座知识模块 */
.horoscope-knowledge {
  margin: var(--space-lg) var(--space-md) var(--space-md);
}

.knowledge-header {
  margin-bottom: var(--space-lg);
}

.knowledge-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.knowledge-list {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.knowledge-item {
  display: flex;
  align-items: center;
  padding: var(--space-lg);
  border-bottom: 1rpx solid var(--bg-secondary);
  transition: background-color 0.3s ease;
}

.knowledge-item:last-child {
  border-bottom: none;
}

.knowledge-item:active {
  background-color: rgba(139, 92, 246, 0.05);
}

.knowledge-icon {
  font-size: 48rpx;
  margin-right: var(--space-lg);
  flex-shrink: 0;
}

.knowledge-content {
  flex: 1;
}

.knowledge-name {
  display: block;
  font-size: var(--text-base);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.knowledge-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.knowledge-arrow {
  font-size: var(--text-lg);
  color: var(--text-light);
  margin-left: var(--space-md);
}

/* 响应式调整 */
@media (max-width: 375px) {
  .function-grid {
    gap: var(--space-sm);
  }

  .function-item {
    padding: var(--space-md);
  }

  .function-icon {
    font-size: 56rpx;
  }
}
