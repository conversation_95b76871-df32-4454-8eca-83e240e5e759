// 日期工具函数
export class DateUtils {
  /**
   * 格式化日期
   */
  static format(date: Date, format: string = 'YYYY-MM-DD'): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  }

  /**
   * 获取今天的日期字符串
   */
  static getToday(): string {
    return this.format(new Date(), 'YYYY-MM-DD');
  }

  /**
   * 获取当前时间戳
   */
  static now(): number {
    return Date.now();
  }

  /**
   * 时间戳转日期
   */
  static fromTimestamp(timestamp: number): Date {
    return new Date(timestamp);
  }

  /**
   * 计算两个日期之间的天数差
   */
  static daysBetween(date1: Date, date2: Date): number {
    const oneDay = 24 * 60 * 60 * 1000;
    return Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay));
  }

  /**
   * 获取相对时间描述
   */
  static getRelativeTime(timestamp: number): string {
    const now = Date.now();
    const diff = now - timestamp;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (seconds < 60) {
      return '刚刚';
    } else if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return this.format(new Date(timestamp), 'MM-DD');
    }
  }

  /**
   * 检查是否是今天
   */
  static isToday(timestamp: number): boolean {
    const today = new Date();
    const date = new Date(timestamp);
    return today.toDateString() === date.toDateString();
  }

  /**
   * 检查是否是本周
   */
  static isThisWeek(timestamp: number): boolean {
    const now = new Date();
    const date = new Date(timestamp);
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    const endOfWeek = new Date(now.setDate(now.getDate() - now.getDay() + 6));
    
    return date >= startOfWeek && date <= endOfWeek;
  }

  /**
   * 获取星期几
   */
  static getWeekday(date: Date): string {
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    return `星期${weekdays[date.getDay()]}`;
  }

  /**
   * 获取农历日期（简化版）
   */
  static getLunarDate(date: Date): string {
    // 这里可以集成农历转换库，暂时返回公历
    return this.format(date, 'MM月DD日');
  }

  /**
   * 解析生日字符串获取月日
   */
  static parseBirthday(birthday: string): { month: number; day: number } | null {
    try {
      // 支持多种格式：YYYY-MM-DD, MM-DD, MM/DD
      const patterns = [
        /^\d{4}-(\d{1,2})-(\d{1,2})$/,  // YYYY-MM-DD
        /^(\d{1,2})-(\d{1,2})$/,        // MM-DD
        /^(\d{1,2})\/(\d{1,2})$/,       // MM/DD
        /^(\d{1,2})月(\d{1,2})日?$/      // MM月DD日
      ];

      for (const pattern of patterns) {
        const match = birthday.match(pattern);
        if (match) {
          const month = parseInt(match[1], 10);
          const day = parseInt(match[2], 10);
          
          if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
            return { month, day };
          }
        }
      }
      
      return null;
    } catch (error) {
      console.error('Parse birthday error:', error);
      return null;
    }
  }
}

// 每日金句数据
export const DAILY_QUOTES = [
  "爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏不完美的人。",
  "真正的爱情是当你们在一起时，整个世界都变得更加美好。",
  "爱是两个人的事，但幸福是一个人的选择。",
  "最好的爱情，是两个人彼此成就，而不是彼此消耗。",
  "爱情就像星星，你看不见它，但它一直在那里闪闪发光。",
  "真爱不是没有分歧，而是即使有分歧也愿意一起解决。",
  "爱情的美好在于，它让我们成为更好的自己。",
  "两个人在一起，最重要的不是相似，而是互补。",
  "爱情需要勇气，去爱一个人，也要有勇气被爱。",
  "最浪漫的事，就是和你一起慢慢变老。",
  "爱情不是占有，而是欣赏；不是改变，而是成长。",
  "真正的爱情，是即使全世界都反对，我们也要在一起。",
  "爱情就像花朵，需要时间和耐心去培育。",
  "最好的关系，是我们可以做自己，而你依然爱我。",
  "爱情不是找到对的人，而是成为对的人。",
  "真爱是当激情褪去后，依然选择在一起。",
  "爱情的力量，能让两颗心跳出同样的节拍。",
  "最深的爱，是在最平凡的日子里，依然觉得对方很特别。",
  "爱情不是1+1=2，而是0.5+0.5=1，两个不完整的人成为一个完整的整体。",
  "真正的爱情，是即使吵架了，也舍不得分开。",
  "爱情就像阳光，温暖而明亮，照亮我们前行的路。",
  "最好的爱情，是我们一起变老，但心依然年轻。",
  "爱情需要信任，就像花朵需要阳光一样。",
  "真爱是当你看到对方的缺点，依然选择爱下去。",
  "爱情的秘诀，是在平凡的日子里创造不平凡的回忆。",
  "最美的爱情，是我们一起成长，一起变得更好。",
  "爱情不是拥有，而是给予；不是索取，而是付出。",
  "真正的爱情，是即使距离很远，心依然很近。",
  "爱情就像音乐，需要两个人一起演奏才能奏出美妙的旋律。",
  "最深的爱，是愿意为对方改变，但不失去自己。"
];

// 获取今日金句
export function getTodayQuote(): string {
  const today = new Date();
  const dayOfYear = Math.floor((today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) / (1000 * 60 * 60 * 24));
  const index = dayOfYear % DAILY_QUOTES.length;
  return DAILY_QUOTES[index];
}
