// 本地存储工具类
export class Storage {
  /**
   * 设置存储数据
   */
  static set(key: string, data: any): void {
    try {
      wx.setStorageSync(key, data);
    } catch (error) {
      console.error('Storage set error:', error);
    }
  }

  /**
   * 获取存储数据
   */
  static get<T>(key: string, defaultValue?: T): T | null {
    try {
      const data = wx.getStorageSync(key);
      return data !== '' ? data : defaultValue || null;
    } catch (error) {
      console.error('Storage get error:', error);
      return defaultValue || null;
    }
  }

  /**
   * 删除存储数据
   */
  static remove(key: string): void {
    try {
      wx.removeStorageSync(key);
    } catch (error) {
      console.error('Storage remove error:', error);
    }
  }

  /**
   * 清空所有存储数据
   */
  static clear(): void {
    try {
      wx.clearStorageSync();
    } catch (error) {
      console.error('Storage clear error:', error);
    }
  }

  /**
   * 获取存储信息
   */
  static getInfo(): WechatMiniprogram.GetStorageInfoSyncResult {
    try {
      return wx.getStorageInfoSync();
    } catch (error) {
      console.error('Storage getInfo error:', error);
      return { keys: [], currentSize: 0, limitSize: 0 };
    }
  }
}

// 存储键名常量
export const STORAGE_KEYS = {
  USER_INFO: 'userInfo',
  USER_HOROSCOPE: 'userHoroscope',
  TAROT_HISTORY: 'tarotHistory',
  TREEHOLE_DRAFTS: 'treeholeDrafts',
  COLLECTIONS: 'collections',
  DAILY_FORTUNE: 'dailyFortune',
  SETTINGS: 'settings'
};

// 用户信息接口
export interface UserInfo {
  nickName: string;
  avatarUrl: string;
  birthday?: string;
  horoscopeSign?: string;
  gender?: 'male' | 'female' | 'unknown';
  createdAt: number;
  lastLoginAt: number;
}

// 塔罗历史记录接口
export interface TarotHistory {
  id: string;
  spreadId: string;
  spreadName: string;
  cards: {
    positionId: string;
    positionName: string;
    cardId: string;
    cardName: string;
    isReversed: boolean;
    interpretation: string;
  }[];
  question?: string;
  createdAt: number;
}

// 收藏接口
export interface Collection {
  id: string;
  type: 'tarot' | 'treehole' | 'fortune';
  title: string;
  content: string;
  createdAt: number;
}

// 树洞草稿接口
export interface TreeholeDraft {
  id: string;
  content: string;
  tags: string[];
  isAnonymous: boolean;
  createdAt: number;
  updatedAt: number;
}

// 设置接口
export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    dailyFortune: boolean;
    tarotReminder: boolean;
    treeholeReply: boolean;
  };
  privacy: {
    showOnline: boolean;
    allowMessages: boolean;
  };
}

// 用户信息管理
export class UserManager {
  static getUserInfo(): UserInfo | null {
    return Storage.get<UserInfo>(STORAGE_KEYS.USER_INFO);
  }

  static setUserInfo(userInfo: UserInfo): void {
    Storage.set(STORAGE_KEYS.USER_INFO, userInfo);
  }

  static updateUserInfo(updates: Partial<UserInfo>): void {
    const currentInfo = this.getUserInfo();
    if (currentInfo) {
      const updatedInfo = { ...currentInfo, ...updates };
      this.setUserInfo(updatedInfo);
    }
  }

  static isLoggedIn(): boolean {
    const userInfo = this.getUserInfo();
    return userInfo !== null && userInfo.nickName !== '';
  }
}

// 塔罗历史管理
export class TarotHistoryManager {
  static getHistory(): TarotHistory[] {
    return Storage.get<TarotHistory[]>(STORAGE_KEYS.TAROT_HISTORY, []);
  }

  static addHistory(history: TarotHistory): void {
    const histories = this.getHistory();
    histories.unshift(history); // 添加到开头
    
    // 限制历史记录数量（最多保存50条）
    if (histories.length > 50) {
      histories.splice(50);
    }
    
    Storage.set(STORAGE_KEYS.TAROT_HISTORY, histories);
  }

  static removeHistory(id: string): void {
    const histories = this.getHistory();
    const filteredHistories = histories.filter(h => h.id !== id);
    Storage.set(STORAGE_KEYS.TAROT_HISTORY, filteredHistories);
  }

  static clearHistory(): void {
    Storage.set(STORAGE_KEYS.TAROT_HISTORY, []);
  }
}

// 收藏管理
export class CollectionManager {
  static getCollections(): Collection[] {
    return Storage.get<Collection[]>(STORAGE_KEYS.COLLECTIONS, []);
  }

  static addCollection(collection: Collection): void {
    const collections = this.getCollections();
    collections.unshift(collection);
    Storage.set(STORAGE_KEYS.COLLECTIONS, collections);
  }

  static removeCollection(id: string): void {
    const collections = this.getCollections();
    const filteredCollections = collections.filter(c => c.id !== id);
    Storage.set(STORAGE_KEYS.COLLECTIONS, filteredCollections);
  }

  static isCollected(id: string): boolean {
    const collections = this.getCollections();
    return collections.some(c => c.id === id);
  }
}

// 设置管理
export class SettingsManager {
  static getSettings(): AppSettings {
    return Storage.get<AppSettings>(STORAGE_KEYS.SETTINGS, {
      theme: 'light',
      notifications: {
        dailyFortune: true,
        tarotReminder: true,
        treeholeReply: true
      },
      privacy: {
        showOnline: true,
        allowMessages: true
      }
    });
  }

  static updateSettings(updates: Partial<AppSettings>): void {
    const currentSettings = this.getSettings();
    const updatedSettings = { ...currentSettings, ...updates };
    Storage.set(STORAGE_KEYS.SETTINGS, updatedSettings);
  }
}
