// 塔罗牌数据配置
export interface TarotCard {
  id: string;
  name: string;
  englishName: string;
  type: 'major' | 'minor'; // 大阿卡纳 | 小阿卡纳
  suit?: string; // 花色（小阿卡纳）
  number?: number;
  image: string;
  keywords: string[];
  uprightMeaning: string;
  reversedMeaning: string;
  loveMeaning: {
    upright: string;
    reversed: string;
  };
}

export interface TarotSpread {
  id: string;
  name: string;
  description: string;
  positions: {
    id: string;
    name: string;
    meaning: string;
    x: number; // 位置坐标
    y: number;
  }[];
}

// 恋爱主题塔罗牌（精选22张大阿卡纳）
export const LOVE_TAROT_CARDS: TarotCard[] = [
  {
    id: 'fool',
    name: '愚者',
    englishName: 'The Fool',
    type: 'major',
    number: 0,
    image: '/assets/tarot/fool.jpg',
    keywords: ['新开始', '冒险', '纯真', '自由'],
    uprightMeaning: '新的开始，充满可能性的旅程',
    reversedMeaning: '鲁莽行事，缺乏计划',
    loveMeaning: {
      upright: '新恋情的开始，保持纯真的心去爱',
      reversed: '在感情中过于冲动，需要更多思考'
    }
  },
  {
    id: 'lovers',
    name: '恋人',
    englishName: 'The Lovers',
    type: 'major',
    number: 6,
    image: '/assets/tarot/lovers.jpg',
    keywords: ['爱情', '选择', '结合', '和谐'],
    uprightMeaning: '真爱，重要的选择，和谐的关系',
    reversedMeaning: '关系不和谐，错误的选择',
    loveMeaning: {
      upright: '真挚的爱情，灵魂伴侣，关系和谐美满',
      reversed: '感情出现分歧，需要重新审视关系'
    }
  },
  {
    id: 'empress',
    name: '皇后',
    englishName: 'The Empress',
    type: 'major',
    number: 3,
    image: '/assets/tarot/empress.jpg',
    keywords: ['母性', '丰饶', '创造', '滋养'],
    uprightMeaning: '丰富的创造力，母性的关怀',
    reversedMeaning: '过度保护，创造力受阻',
    loveMeaning: {
      upright: '温柔的爱情，充满关怀和滋养的关系',
      reversed: '在感情中过于依赖或控制'
    }
  },
  {
    id: 'emperor',
    name: '皇帝',
    englishName: 'The Emperor',
    type: 'major',
    number: 4,
    image: '/assets/tarot/emperor.jpg',
    keywords: ['权威', '稳定', '保护', '领导'],
    uprightMeaning: '稳定的基础，强有力的保护',
    reversedMeaning: '过度控制，缺乏灵活性',
    loveMeaning: {
      upright: '稳定可靠的伴侣，给予安全感的关系',
      reversed: '感情中存在控制欲或权力斗争'
    }
  },
  {
    id: 'hierophant',
    name: '教皇',
    englishName: 'The Hierophant',
    type: 'major',
    number: 5,
    image: '/assets/tarot/hierophant.jpg',
    keywords: ['传统', '信仰', '指导', '承诺'],
    uprightMeaning: '传统价值，精神指导',
    reversedMeaning: '反叛传统，缺乏信仰',
    loveMeaning: {
      upright: '传统的爱情观，认真的承诺关系',
      reversed: '不愿意承诺，反对传统的爱情模式'
    }
  },
  {
    id: 'wheel-of-fortune',
    name: '命运之轮',
    englishName: 'Wheel of Fortune',
    type: 'major',
    number: 10,
    image: '/assets/tarot/wheel-of-fortune.jpg',
    keywords: ['命运', '变化', '循环', '机遇'],
    uprightMeaning: '好运降临，命运的转机',
    reversedMeaning: '运气不佳，失去控制',
    loveMeaning: {
      upright: '感情迎来转机，命中注定的相遇',
      reversed: '感情起伏不定，需要耐心等待'
    }
  },
  {
    id: 'strength',
    name: '力量',
    englishName: 'Strength',
    type: 'major',
    number: 8,
    image: '/assets/tarot/strength.jpg',
    keywords: ['内在力量', '勇气', '耐心', '温柔'],
    uprightMeaning: '内在的力量，温柔的坚持',
    reversedMeaning: '缺乏自信，内心软弱',
    loveMeaning: {
      upright: '用温柔和耐心经营感情，内心强大',
      reversed: '在感情中缺乏自信，需要更多勇气'
    }
  },
  {
    id: 'hermit',
    name: '隐者',
    englishName: 'The Hermit',
    type: 'major',
    number: 9,
    image: '/assets/tarot/hermit.jpg',
    keywords: ['内省', '寻找', '智慧', '孤独'],
    uprightMeaning: '内心的探索，寻找真理',
    reversedMeaning: '过度孤立，拒绝指导',
    loveMeaning: {
      upright: '需要独处思考感情，寻找内心的答案',
      reversed: '在感情中过于封闭，需要敞开心扉'
    }
  },
  {
    id: 'justice',
    name: '正义',
    englishName: 'Justice',
    type: 'major',
    number: 11,
    image: '/assets/tarot/justice.jpg',
    keywords: ['公正', '平衡', '真相', '因果'],
    uprightMeaning: '公正的判断，平衡的关系',
    reversedMeaning: '不公正，失去平衡',
    loveMeaning: {
      upright: '感情中的公平对待，理性的选择',
      reversed: '感情不平等，需要重新平衡关系'
    }
  },
  {
    id: 'hanged-man',
    name: '倒吊人',
    englishName: 'The Hanged Man',
    type: 'major',
    number: 12,
    image: '/assets/tarot/hanged-man.jpg',
    keywords: ['牺牲', '等待', '换位思考', '暂停'],
    uprightMeaning: '必要的牺牲，换个角度看问题',
    reversedMeaning: '无谓的牺牲，拒绝改变',
    loveMeaning: {
      upright: '为爱情做出牺牲，耐心等待时机',
      reversed: '在感情中过度牺牲自己，失去自我'
    }
  }
];

// 塔罗牌阵配置
export const TAROT_SPREADS: TarotSpread[] = [
  {
    id: 'single-card',
    name: '单张指引',
    description: '最简单的占卜方式，获得当下的指引',
    positions: [
      {
        id: 'guidance',
        name: '指引',
        meaning: '当前情况的指引和建议',
        x: 50,
        y: 50
      }
    ]
  },
  {
    id: 'love-triangle',
    name: '恋爱三角',
    description: '了解你的感情现状、对方想法和未来发展',
    positions: [
      {
        id: 'your-feelings',
        name: '你的感受',
        meaning: '你对这段感情的真实感受',
        x: 30,
        y: 70
      },
      {
        id: 'their-feelings',
        name: '对方想法',
        meaning: '对方对你和这段关系的想法',
        x: 70,
        y: 70
      },
      {
        id: 'future',
        name: '未来发展',
        meaning: '这段关系的未来发展趋势',
        x: 50,
        y: 30
      }
    ]
  },
  {
    id: 'relationship-cross',
    name: '关系十字',
    description: '深入了解一段关系的各个方面',
    positions: [
      {
        id: 'present',
        name: '现状',
        meaning: '关系的当前状态',
        x: 50,
        y: 50
      },
      {
        id: 'challenge',
        name: '挑战',
        meaning: '关系面临的主要挑战',
        x: 50,
        y: 20
      },
      {
        id: 'past',
        name: '过去',
        meaning: '影响关系的过去因素',
        x: 20,
        y: 50
      },
      {
        id: 'future',
        name: '未来',
        meaning: '关系的发展方向',
        x: 80,
        y: 50
      },
      {
        id: 'advice',
        name: '建议',
        meaning: '改善关系的建议',
        x: 50,
        y: 80
      }
    ]
  }
];

// 随机抽取塔罗牌
export function drawRandomCard(): TarotCard {
  const randomIndex = Math.floor(Math.random() * LOVE_TAROT_CARDS.length);
  return LOVE_TAROT_CARDS[randomIndex];
}

// 随机抽取多张塔罗牌（不重复）
export function drawMultipleCards(count: number): TarotCard[] {
  const shuffled = [...LOVE_TAROT_CARDS].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, Math.min(count, LOVE_TAROT_CARDS.length));
}
