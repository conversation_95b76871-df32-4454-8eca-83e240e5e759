// 星座数据配置
export interface HoroscopeSign {
  id: string;
  name: string;
  englishName: string;
  symbol: string;
  element: string; // 火、土、风、水
  dateRange: string;
  personality: string[];
  loveTraits: string[];
  compatibility: string[]; // 最配星座
}

export const HOROSCOPE_SIGNS: HoroscopeSign[] = [
  {
    id: 'aries',
    name: '白羊座',
    englishName: 'Aries',
    symbol: '♈',
    element: '火',
    dateRange: '3月21日-4月19日',
    personality: ['热情主动', '勇敢直率', '冲动急躁', '领导力强'],
    loveTraits: ['主动追求', '热情如火', '直来直去', '保护欲强'],
    compatibility: ['狮子座', '射手座', '双子座']
  },
  {
    id: 'taurus',
    name: '金牛座',
    englishName: 'Taurus',
    symbol: '♉',
    element: '土',
    dateRange: '4月20日-5月20日',
    personality: ['稳重踏实', '固执保守', '享受生活', '责任感强'],
    loveTraits: ['慢热专一', '重视安全感', '浪漫务实', '占有欲强'],
    compatibility: ['处女座', '摩羯座', '巨蟹座']
  },
  {
    id: 'gemini',
    name: '双子座',
    englishName: 'Gemini',
    symbol: '♊',
    element: '风',
    dateRange: '5月21日-6月21日',
    personality: ['聪明机智', '善于沟通', '多变好奇', '适应力强'],
    loveTraits: ['风趣幽默', '需要新鲜感', '善于表达', '理性多于感性'],
    compatibility: ['天秤座', '水瓶座', '白羊座']
  },
  {
    id: 'cancer',
    name: '巨蟹座',
    englishName: 'Cancer',
    symbol: '♋',
    element: '水',
    dateRange: '6月22日-7月22日',
    personality: ['温柔体贴', '情感丰富', '敏感多疑', '家庭观念强'],
    loveTraits: ['深情专一', '需要安全感', '善于照顾人', '情绪化'],
    compatibility: ['天蝎座', '双鱼座', '金牛座']
  },
  {
    id: 'leo',
    name: '狮子座',
    englishName: 'Leo',
    symbol: '♌',
    element: '火',
    dateRange: '7月23日-8月22日',
    personality: ['自信大方', '慷慨热情', '爱面子', '领导欲强'],
    loveTraits: ['浪漫大方', '需要被崇拜', '忠诚专一', '占有欲强'],
    compatibility: ['白羊座', '射手座', '双子座']
  },
  {
    id: 'virgo',
    name: '处女座',
    englishName: 'Virgo',
    symbol: '♍',
    element: '土',
    dateRange: '8月23日-9月22日',
    personality: ['完美主义', '细心谨慎', '挑剔严格', '服务精神'],
    loveTraits: ['慢热内敛', '重视细节', '忠诚可靠', '要求完美'],
    compatibility: ['金牛座', '摩羯座', '天蝎座']
  },
  {
    id: 'libra',
    name: '天秤座',
    englishName: 'Libra',
    symbol: '♎',
    element: '风',
    dateRange: '9月23日-10月23日',
    personality: ['优雅和谐', '犹豫不决', '社交能力强', '追求平衡'],
    loveTraits: ['浪漫优雅', '重视外表', '需要陪伴', '善于妥协'],
    compatibility: ['双子座', '水瓶座', '狮子座']
  },
  {
    id: 'scorpio',
    name: '天蝎座',
    englishName: 'Scorpio',
    symbol: '♏',
    element: '水',
    dateRange: '10月24日-11月22日',
    personality: ['神秘深沉', '意志坚强', '占有欲强', '洞察力强'],
    loveTraits: ['深情专一', '占有欲极强', '爱恨分明', '神秘魅力'],
    compatibility: ['巨蟹座', '双鱼座', '处女座']
  },
  {
    id: 'sagittarius',
    name: '射手座',
    englishName: 'Sagittarius',
    symbol: '♐',
    element: '火',
    dateRange: '11月23日-12月21日',
    personality: ['自由乐观', '爱冒险', '直率坦诚', '哲学思维'],
    loveTraits: ['自由恋爱', '乐观开朗', '不喜束缚', '真诚直接'],
    compatibility: ['白羊座', '狮子座', '水瓶座']
  },
  {
    id: 'capricorn',
    name: '摩羯座',
    englishName: 'Capricorn',
    symbol: '♑',
    element: '土',
    dateRange: '12月22日-1月19日',
    personality: ['务实稳重', '有责任心', '保守传统', '目标明确'],
    loveTraits: ['慢热专一', '重视承诺', '务实理性', '责任感强'],
    compatibility: ['金牛座', '处女座', '天蝎座']
  },
  {
    id: 'aquarius',
    name: '水瓶座',
    englishName: 'Aquarius',
    symbol: '♒',
    element: '风',
    dateRange: '1月20日-2月18日',
    personality: ['独立创新', '理性客观', '友善博爱', '特立独行'],
    loveTraits: ['理性恋爱', '需要空间', '友情式爱情', '不喜束缚'],
    compatibility: ['双子座', '天秤座', '射手座']
  },
  {
    id: 'pisces',
    name: '双鱼座',
    englishName: 'Pisces',
    symbol: '♓',
    element: '水',
    dateRange: '2月19日-3月20日',
    personality: ['温柔浪漫', '富有想象力', '敏感多情', '同情心强'],
    loveTraits: ['浪漫多情', '容易动情', '需要呵护', '理想主义'],
    compatibility: ['巨蟹座', '天蝎座', '摩羯座']
  }
];

// 根据生日获取星座
export function getHoroscopeByDate(month: number, day: number): HoroscopeSign | null {
  const dateRanges = [
    { sign: 'capricorn', start: [12, 22], end: [1, 19] },
    { sign: 'aquarius', start: [1, 20], end: [2, 18] },
    { sign: 'pisces', start: [2, 19], end: [3, 20] },
    { sign: 'aries', start: [3, 21], end: [4, 19] },
    { sign: 'taurus', start: [4, 20], end: [5, 20] },
    { sign: 'gemini', start: [5, 21], end: [6, 21] },
    { sign: 'cancer', start: [6, 22], end: [7, 22] },
    { sign: 'leo', start: [7, 23], end: [8, 22] },
    { sign: 'virgo', start: [8, 23], end: [9, 22] },
    { sign: 'libra', start: [9, 23], end: [10, 23] },
    { sign: 'scorpio', start: [10, 24], end: [11, 22] },
    { sign: 'sagittarius', start: [11, 23], end: [12, 21] }
  ];

  for (const range of dateRanges) {
    const [startMonth, startDay] = range.start;
    const [endMonth, endDay] = range.end;
    
    if (startMonth === endMonth) {
      if (month === startMonth && day >= startDay && day <= endDay) {
        return HOROSCOPE_SIGNS.find(sign => sign.id === range.sign) || null;
      }
    } else {
      if ((month === startMonth && day >= startDay) || 
          (month === endMonth && day <= endDay)) {
        return HOROSCOPE_SIGNS.find(sign => sign.id === range.sign) || null;
      }
    }
  }
  
  return null;
}
